using UnityEngine;

/// <summary>
/// 地图配置 - 存储地图相关的配置信息
/// </summary>
[CreateAssetMenu(fileName = "MapConfig", menuName = "Map/Map Config")]
public class MapConfig : ScriptableObject
{
    [Header("常熟市地图配置")]
    [Toolt<PERSON>("常熟市中心坐标（经度，纬度）")]
    public Vector2 changshuCenter = new Vector2(120.7478f, 31.6544f);
    
    [<PERSON>lt<PERSON>("默认缩放级别")]
    public float defaultZoom = 10f;
    
    [Tooltip("最小缩放级别")]
    public float minZoom = 3f;
    
    [<PERSON>lt<PERSON>("最大缩放级别")]
    public float maxZoom = 18f;
    
    [Header("重要地点")]
    [<PERSON>lt<PERSON>("常熟市重要地点列表")]
    public MapLocation[] importantLocations = new MapLocation[]
    {
        new MapLocation { name = "常熟市政府", coordinate = new Vector2(120.7478f, 31.6544f), description = "常熟市人民政府" },
        new MapLocation { name = "常熟理工学院", coordinate = new Vector2(120.7058f, 31.6739f), description = "常熟理工学院" },
        new MapLocation { name = "常熟火车站", coordinate = new Vector2(120.7333f, 31.6667f), description = "常熟火车站" },
        new MapLocation { name = "虞山", coordinate = new Vector2(120.7167f, 31.6833f), description = "虞山风景区" },
        new MapLocation { name = "尚湖", coordinate = new Vector2(120.7000f, 31.7000f), description = "尚湖风景区" },
        new MapLocation { name = "方塔园", coordinate = new Vector2(120.7444f, 31.6556f), description = "方塔园景区" },
        new MapLocation { name = "常熟博物馆", coordinate = new Vector2(120.7500f, 31.6500f), description = "常熟博物馆" },
        new MapLocation { name = "沙家浜", coordinate = new Vector2(120.8167f, 31.6167f), description = "沙家浜风景区" }
    };
    
    [Header("地图样式配置")]
    [Tooltip("地图瓦片大小")]
    public int tileSize = 256;
    
    [Tooltip("瓦片加载半径")]
    public int loadRadius = 2;
    
    [Tooltip("最大缓存瓦片数量")]
    public int maxCacheSize = 100;
    
    [Header("交互配置")]
    [Tooltip("拖拽灵敏度")]
    public float dragSensitivity = 1f;
    
    [Tooltip("缩放灵敏度")]
    public float zoomSensitivity = 0.5f;
    
    [Tooltip("平滑移动时间")]
    public float smoothTime = 0.3f;
    
    [Header("API配置")]
    [Tooltip("高德地图API密钥")]
    public string aMapApiKey = "YOUR_AMAP_API_KEY";
    
    [Tooltip("是否启用地理编码功能")]
    public bool enableGeocoding = true;
    
    [Tooltip("是否启用逆地理编码功能")]
    public bool enableReverseGeocoding = true;
    
    /// <summary>
    /// 获取指定名称的地点
    /// </summary>
    public MapLocation GetLocationByName(string name)
    {
        foreach (var location in importantLocations)
        {
            if (location.name.Equals(name, System.StringComparison.OrdinalIgnoreCase))
            {
                return location;
            }
        }
        return null;
    }
    
    /// <summary>
    /// 获取距离指定坐标最近的地点
    /// </summary>
    public MapLocation GetNearestLocation(Vector2 coordinate)
    {
        MapLocation nearest = null;
        float minDistance = float.MaxValue;
        
        foreach (var location in importantLocations)
        {
            float distance = Vector2.Distance(coordinate, location.coordinate);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = location;
            }
        }
        
        return nearest;
    }
    
    /// <summary>
    /// 验证配置
    /// </summary>
    public bool ValidateConfig()
    {
        if (minZoom >= maxZoom)
        {
            Debug.LogError("最小缩放级别不能大于等于最大缩放级别");
            return false;
        }
        
        if (defaultZoom < minZoom || defaultZoom > maxZoom)
        {
            Debug.LogError("默认缩放级别必须在最小和最大缩放级别之间");
            return false;
        }
        
        if (tileSize <= 0)
        {
            Debug.LogError("瓦片大小必须大于0");
            return false;
        }
        
        if (loadRadius < 1)
        {
            Debug.LogError("加载半径必须大于等于1");
            return false;
        }
        
        if (maxCacheSize < 10)
        {
            Debug.LogError("最大缓存大小建议不少于10");
            return false;
        }
        
        return true;
    }
}

/// <summary>
/// 地图地点数据结构
/// </summary>
[System.Serializable]
public class MapLocation
{
    [Tooltip("地点名称")]
    public string name;
    
    [Tooltip("地点坐标（经度，纬度）")]
    public Vector2 coordinate;
    
    [Tooltip("地点描述")]
    public string description;
    
    [Tooltip("地点类型")]
    public LocationType type = LocationType.General;
    
    [Tooltip("是否显示在地图上")]
    public bool showOnMap = true;
    
    [Tooltip("地点图标")]
    public Sprite icon;
}

/// <summary>
/// 地点类型枚举
/// </summary>
public enum LocationType
{
    General,        // 一般地点
    Government,     // 政府机构
    Education,      // 教育机构
    Transportation, // 交通设施
    Tourism,        // 旅游景点
    Culture,        // 文化场所
    Commercial,     // 商业区域
    Residential     // 居住区域
}
