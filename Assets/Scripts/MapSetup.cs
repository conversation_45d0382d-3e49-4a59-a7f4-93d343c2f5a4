using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 地图设置助手 - 帮助快速设置地图系统
/// </summary>
public class MapSetup : MonoBehaviour
{
    [Header("自动设置选项")]
    public bool autoSetupOnStart = true;
    public bool createUICanvas = true;
    public bool createMapCamera = true;
    
    [Header("地图配置")]
    public MapConfig mapConfig;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupMapSystem();
        }
    }
    
    /// <summary>
    /// 设置地图系统
    /// </summary>
    [ContextMenu("Setup Map System")]
    public void SetupMapSystem()
    {
        // 创建地图配置
        if (mapConfig == null)
        {
            CreateDefaultMapConfig();
        }
        
        // 设置地图管理器
        SetupMapManager();
        
        // 创建地图相机
        if (createMapCamera)
        {
            SetupMapCamera();
        }
        
        // 创建UI画布
        if (createUICanvas)
        {
            SetupUICanvas();
        }
        
        Debug.Log("地图系统设置完成！");
    }
    
    /// <summary>
    /// 创建默认地图配置
    /// </summary>
    private void CreateDefaultMapConfig()
    {
        mapConfig = ScriptableObject.CreateInstance<MapConfig>();
        
        // 设置常熟市默认配置
        mapConfig.changshuCenter = new Vector2(120.7478f, 31.6544f);
        mapConfig.defaultZoom = 10f;
        mapConfig.minZoom = 3f;
        mapConfig.maxZoom = 18f;
        
        Debug.Log("已创建默认地图配置");
    }
    
    /// <summary>
    /// 设置地图管理器
    /// </summary>
    private void SetupMapManager()
    {
        MapManager mapManager = GetComponent<MapManager>();
        if (mapManager == null)
        {
            mapManager = gameObject.AddComponent<MapManager>();
        }
        
        // 设置地图配置
        mapManager.mapConfig = mapConfig;
        
        // 创建地图容器
        if (mapManager.mapContainer == null)
        {
            GameObject mapContainer = new GameObject("MapContainer");
            mapContainer.transform.SetParent(transform);
            mapContainer.transform.localPosition = Vector3.zero;
            mapManager.mapContainer = mapContainer.transform;
        }
        
        Debug.Log("地图管理器设置完成");
    }
    
    /// <summary>
    /// 设置地图相机
    /// </summary>
    private void SetupMapCamera()
    {
        MapManager mapManager = GetComponent<MapManager>();
        if (mapManager == null) return;
        
        if (mapManager.mapCamera == null)
        {
            // 创建地图相机
            GameObject cameraObj = new GameObject("MapCamera");
            cameraObj.transform.SetParent(transform);
            cameraObj.transform.localPosition = new Vector3(0, 0, -10);
            
            Camera camera = cameraObj.AddComponent<Camera>();
            camera.orthographic = true;
            camera.orthographicSize = 5;
            camera.backgroundColor = Color.blue;
            
            mapManager.mapCamera = camera;
        }
        
        Debug.Log("地图相机设置完成");
    }
    
    /// <summary>
    /// 设置UI画布
    /// </summary>
    private void SetupUICanvas()
    {
        // 查找现有画布
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            // 创建新画布
            GameObject canvasObj = new GameObject("MapUICanvas");
            canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            
            CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            
            canvasObj.AddComponent<GraphicRaycaster>();
        }
        
        // 创建UI组件
        CreateMapUI(canvas);
        
        Debug.Log("UI画布设置完成");
    }
    
    /// <summary>
    /// 创建地图UI
    /// </summary>
    private void CreateMapUI(Canvas canvas)
    {
        MapManager mapManager = GetComponent<MapManager>();
        if (mapManager == null) return;
        
        // 创建主UI面板
        GameObject mainPanel = CreateUIPanel("MainPanel", canvas.transform);
        
        // 创建控制按钮
        CreateControlButtons(mainPanel.transform, mapManager);
        
        // 创建信息显示
        CreateInfoDisplay(mainPanel.transform, mapManager);
        
        // 创建缩放滑块
        CreateZoomSlider(mainPanel.transform, mapManager);
        
        // 添加MapUI组件
        MapUI mapUI = canvas.gameObject.AddComponent<MapUI>();
        mapUI.mainPanel = mainPanel;
    }
    
    /// <summary>
    /// 创建UI面板
    /// </summary>
    private GameObject CreateUIPanel(string name, Transform parent)
    {
        GameObject panel = new GameObject(name);
        panel.transform.SetParent(parent);
        
        RectTransform rect = panel.AddComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;
        
        Image image = panel.AddComponent<Image>();
        image.color = new Color(0, 0, 0, 0.1f);
        
        return panel;
    }
    
    /// <summary>
    /// 创建控制按钮
    /// </summary>
    private void CreateControlButtons(Transform parent, MapManager mapManager)
    {
        // 放大按钮
        GameObject zoomInBtn = CreateButton("ZoomInButton", "+", parent);
        SetButtonPosition(zoomInBtn, new Vector2(-100, 100), new Vector2(50, 50));
        
        // 缩小按钮
        GameObject zoomOutBtn = CreateButton("ZoomOutButton", "-", parent);
        SetButtonPosition(zoomOutBtn, new Vector2(-100, 40), new Vector2(50, 50));
        
        // 重置按钮
        GameObject resetBtn = CreateButton("ResetButton", "重置", parent);
        SetButtonPosition(resetBtn, new Vector2(-100, -20), new Vector2(80, 50));
        
        // 设置按钮引用
        mapManager.resetButton = resetBtn.GetComponent<Button>();
    }
    
    /// <summary>
    /// 创建信息显示
    /// </summary>
    private void CreateInfoDisplay(Transform parent, MapManager mapManager)
    {
        // 坐标显示
        GameObject coordText = CreateText("CoordinateText", "坐标: 0, 0", parent);
        SetTextPosition(coordText, new Vector2(100, 100), new Vector2(200, 30));
        
        mapManager.coordinateText = coordText.GetComponent<Text>();
    }
    
    /// <summary>
    /// 创建缩放滑块
    /// </summary>
    private void CreateZoomSlider(Transform parent, MapManager mapManager)
    {
        GameObject sliderObj = new GameObject("ZoomSlider");
        sliderObj.transform.SetParent(parent);
        
        RectTransform rect = sliderObj.AddComponent<RectTransform>();
        rect.anchoredPosition = new Vector2(0, -100);
        rect.sizeDelta = new Vector2(300, 20);
        
        Slider slider = sliderObj.AddComponent<Slider>();
        slider.minValue = 3f;
        slider.maxValue = 18f;
        slider.value = 10f;
        
        // 创建滑块背景
        GameObject background = new GameObject("Background");
        background.transform.SetParent(sliderObj.transform);
        background.AddComponent<Image>().color = Color.gray;
        
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;
        
        // 创建滑块手柄
        GameObject handle = new GameObject("Handle");
        handle.transform.SetParent(sliderObj.transform);
        handle.AddComponent<Image>().color = Color.white;
        
        RectTransform handleRect = handle.GetComponent<RectTransform>();
        handleRect.sizeDelta = new Vector2(20, 20);
        
        slider.targetGraphic = handle.GetComponent<Image>();
        slider.handleRect = handleRect;
        
        mapManager.zoomSlider = slider;
    }
    
    /// <summary>
    /// 创建按钮
    /// </summary>
    private GameObject CreateButton(string name, string text, Transform parent)
    {
        GameObject button = new GameObject(name);
        button.transform.SetParent(parent);
        
        button.AddComponent<Image>().color = Color.white;
        Button btn = button.AddComponent<Button>();
        
        // 创建文本
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(button.transform);
        
        Text textComp = textObj.AddComponent<Text>();
        textComp.text = text;
        textComp.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComp.color = Color.black;
        textComp.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        return button;
    }
    
    /// <summary>
    /// 创建文本
    /// </summary>
    private GameObject CreateText(string name, string text, Transform parent)
    {
        GameObject textObj = new GameObject(name);
        textObj.transform.SetParent(parent);
        
        Text textComp = textObj.AddComponent<Text>();
        textComp.text = text;
        textComp.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComp.color = Color.white;
        textComp.fontSize = 14;
        
        return textObj;
    }
    
    /// <summary>
    /// 设置按钮位置
    /// </summary>
    private void SetButtonPosition(GameObject button, Vector2 position, Vector2 size)
    {
        RectTransform rect = button.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(1, 1);
        rect.anchorMax = new Vector2(1, 1);
        rect.anchoredPosition = position;
        rect.sizeDelta = size;
    }
    
    /// <summary>
    /// 设置文本位置
    /// </summary>
    private void SetTextPosition(GameObject text, Vector2 position, Vector2 size)
    {
        RectTransform rect = text.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(0, 1);
        rect.anchorMax = new Vector2(0, 1);
        rect.anchoredPosition = position;
        rect.sizeDelta = size;
    }
}
