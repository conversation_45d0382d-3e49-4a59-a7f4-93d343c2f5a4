using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// 地图UI管理器 - 管理地图相关的用户界面
/// </summary>
public class MapUI : MonoBehaviour
{
    [Header("UI面板")]
    public GameObject mainPanel;
    public GameObject locationPanel;
    public GameObject settingsPanel;
    
    [Header("控制按钮")]
    public Button zoomInButton;
    public Button zoomOutButton;
    public Button resetButton;
    public Button locationsButton;
    public Button settingsButton;
    
    [Header("信息显示")]
    public Text coordinateText;
    public Text zoomText;
    public Text addressText;
    public Text cacheInfoText;
    
    [Header("缩放控制")]
    public Slider zoomSlider;
    
    [Header("地点列表")]
    public Transform locationListParent;
    public GameObject locationItemPrefab;
    
    [Header("设置选项")]
    public Toggle enableGeocodingToggle;
    public Toggle showCoordinatesToggle;
    public InputField apiKeyInput;
    
    // 组件引用
    private MapManager mapManager;
    private MapConfig mapConfig;
    private List<GameObject> locationItems = new List<GameObject>();
    
    void Start()
    {
        InitializeUI();
    }
    
    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        // 获取地图管理器
        mapManager = FindObjectOfType<MapManager>();
        if (mapManager != null)
        {
            mapConfig = mapManager.mapConfig;
        }
        
        // 设置按钮事件
        SetupButtonEvents();
        
        // 设置滑块事件
        SetupSliderEvents();
        
        // 设置切换开关事件
        SetupToggleEvents();
        
        // 初始化地点列表
        InitializeLocationList();
        
        // 初始化设置面板
        InitializeSettingsPanel();
        
        // 默认隐藏子面板
        if (locationPanel != null) locationPanel.SetActive(false);
        if (settingsPanel != null) settingsPanel.SetActive(false);
    }
    
    /// <summary>
    /// 设置按钮事件
    /// </summary>
    private void SetupButtonEvents()
    {
        if (zoomInButton != null)
            zoomInButton.onClick.AddListener(() => mapManager?.GetComponent<MapController>()?.ZoomIn());
        
        if (zoomOutButton != null)
            zoomOutButton.onClick.AddListener(() => mapManager?.GetComponent<MapController>()?.ZoomOut());
        
        if (resetButton != null)
            resetButton.onClick.AddListener(() => mapManager?.ResetMapView());
        
        if (locationsButton != null)
            locationsButton.onClick.AddListener(ToggleLocationPanel);
        
        if (settingsButton != null)
            settingsButton.onClick.AddListener(ToggleSettingsPanel);
    }
    
    /// <summary>
    /// 设置滑块事件
    /// </summary>
    private void SetupSliderEvents()
    {
        if (zoomSlider != null && mapManager != null)
        {
            zoomSlider.minValue = mapManager.MinZoom;
            zoomSlider.maxValue = mapManager.MaxZoom;
            zoomSlider.value = mapManager.currentZoom;
            zoomSlider.onValueChanged.AddListener(OnZoomSliderChanged);
        }
    }
    
    /// <summary>
    /// 设置切换开关事件
    /// </summary>
    private void SetupToggleEvents()
    {
        if (enableGeocodingToggle != null)
        {
            enableGeocodingToggle.isOn = mapConfig?.enableGeocoding ?? true;
            enableGeocodingToggle.onValueChanged.AddListener(OnGeocodingToggleChanged);
        }
        
        if (showCoordinatesToggle != null)
        {
            showCoordinatesToggle.isOn = true;
            showCoordinatesToggle.onValueChanged.AddListener(OnShowCoordinatesToggleChanged);
        }
    }
    
    /// <summary>
    /// 初始化地点列表
    /// </summary>
    private void InitializeLocationList()
    {
        if (locationListParent == null || locationItemPrefab == null || mapConfig == null)
            return;
        
        // 清除现有项目
        ClearLocationItems();
        
        // 创建地点项目
        foreach (var location in mapConfig.importantLocations)
        {
            if (location.showOnMap)
            {
                CreateLocationItem(location);
            }
        }
    }
    
    /// <summary>
    /// 创建地点项目
    /// </summary>
    private void CreateLocationItem(MapLocation location)
    {
        GameObject item = Instantiate(locationItemPrefab, locationListParent);
        
        // 设置文本
        Text nameText = item.transform.Find("NameText")?.GetComponent<Text>();
        if (nameText != null) nameText.text = location.name;
        
        Text descText = item.transform.Find("DescriptionText")?.GetComponent<Text>();
        if (descText != null) descText.text = location.description;
        
        // 设置按钮事件
        Button button = item.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.AddListener(() => NavigateToLocation(location));
        }
        
        locationItems.Add(item);
    }
    
    /// <summary>
    /// 清除地点项目
    /// </summary>
    private void ClearLocationItems()
    {
        foreach (var item in locationItems)
        {
            if (item != null)
                DestroyImmediate(item);
        }
        locationItems.Clear();
    }
    
    /// <summary>
    /// 初始化设置面板
    /// </summary>
    private void InitializeSettingsPanel()
    {
        if (apiKeyInput != null && mapConfig != null)
        {
            apiKeyInput.text = mapConfig.aMapApiKey;
            apiKeyInput.onEndEdit.AddListener(OnApiKeyChanged);
        }
    }
    
    /// <summary>
    /// 导航到指定地点
    /// </summary>
    private void NavigateToLocation(MapLocation location)
    {
        if (mapManager != null)
        {
            MapController controller = mapManager.GetComponent<MapController>();
            controller?.MoveToWithZoom(location.coordinate, 15f);
        }
        
        // 隐藏地点面板
        if (locationPanel != null)
            locationPanel.SetActive(false);
    }
    
    /// <summary>
    /// 切换地点面板
    /// </summary>
    private void ToggleLocationPanel()
    {
        if (locationPanel != null)
        {
            bool isActive = !locationPanel.activeSelf;
            locationPanel.SetActive(isActive);
            
            // 隐藏其他面板
            if (isActive && settingsPanel != null)
                settingsPanel.SetActive(false);
        }
    }
    
    /// <summary>
    /// 切换设置面板
    /// </summary>
    private void ToggleSettingsPanel()
    {
        if (settingsPanel != null)
        {
            bool isActive = !settingsPanel.activeSelf;
            settingsPanel.SetActive(isActive);
            
            // 隐藏其他面板
            if (isActive && locationPanel != null)
                locationPanel.SetActive(false);
        }
    }
    
    /// <summary>
    /// 缩放滑块变化事件
    /// </summary>
    private void OnZoomSliderChanged(float value)
    {
        mapManager?.UpdateZoom(value);
    }
    
    /// <summary>
    /// 地理编码切换事件
    /// </summary>
    private void OnGeocodingToggleChanged(bool value)
    {
        if (mapConfig != null)
        {
            mapConfig.enableGeocoding = value;
        }
    }
    
    /// <summary>
    /// 显示坐标切换事件
    /// </summary>
    private void OnShowCoordinatesToggleChanged(bool value)
    {
        if (coordinateText != null)
        {
            coordinateText.gameObject.SetActive(value);
        }
    }
    
    /// <summary>
    /// API密钥变化事件
    /// </summary>
    private void OnApiKeyChanged(string value)
    {
        if (mapConfig != null)
        {
            mapConfig.aMapApiKey = value;
            
            // 更新API组件
            AMapAPI aMapAPI = mapManager?.GetComponent<AMapAPI>();
            aMapAPI?.SetApiKey(value);
        }
    }
    
    void Update()
    {
        UpdateUI();
    }
    
    /// <summary>
    /// 更新UI显示
    /// </summary>
    private void UpdateUI()
    {
        if (mapManager == null) return;
        
        // 更新坐标显示
        if (coordinateText != null)
        {
            coordinateText.text = $"经度: {mapManager.currentCenter.x:F4}\n纬度: {mapManager.currentCenter.y:F4}";
        }
        
        // 更新缩放显示
        if (zoomText != null)
        {
            zoomText.text = $"缩放: {mapManager.currentZoom:F1}";
        }
        
        // 更新缩放滑块
        if (zoomSlider != null && Mathf.Abs(zoomSlider.value - mapManager.currentZoom) > 0.1f)
        {
            zoomSlider.value = mapManager.currentZoom;
        }
        
        // 更新缓存信息
        if (cacheInfoText != null)
        {
            MapTileSystem tileSystem = mapManager.GetComponent<MapTileSystem>();
            if (tileSystem != null)
            {
                cacheInfoText.text = tileSystem.GetCacheInfo();
            }
        }
    }
    
    /// <summary>
    /// 更新地址显示
    /// </summary>
    public void UpdateAddressDisplay(string address)
    {
        if (addressText != null)
        {
            addressText.text = address;
        }
    }
}
