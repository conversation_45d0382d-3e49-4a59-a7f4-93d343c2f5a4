using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 地图管理器 - 负责管理整个2D地图系统
/// </summary>
public class MapManager : MonoBehaviour
{
    [Header("地图配置")]
    public MapConfig mapConfig;
    
    [Header("地图组件")]
    public Transform mapContainer;
    public Camera mapCamera;
    
    [Header("UI组件")]
    public Slider zoomSlider;
    public Text coordinateText;
    public Button resetButton;
    
    [Header("地图状态")]
    public float currentZoom = 10f;
    public Vector2 currentCenter = new Vector2(120.7478f, 31.6544f); // 常熟市中心坐标
    
    // 私有组件
    private AMapAPI aMapAPI;
    private MapTileSystem tileSystem;
    private MapController mapController;
    
    // 地图参数
    private float minZoom = 3f;
    private float maxZoom = 18f;
    private int tileSize = 256;
    
    void Start()
    {
        InitializeMapSystem();
    }
    
    /// <summary>
    /// 初始化地图系统
    /// </summary>
    private void InitializeMapSystem()
    {
        // 初始化API组件
        aMapAPI = GetComponent<AMapAPI>();
        if (aMapAPI == null)
        {
            aMapAPI = gameObject.AddComponent<AMapAPI>();
        }
        
        // 初始化瓦片系统
        tileSystem = GetComponent<MapTileSystem>();
        if (tileSystem == null)
        {
            tileSystem = gameObject.AddComponent<MapTileSystem>();
        }
        
        // 初始化控制器
        mapController = GetComponent<MapController>();
        if (mapController == null)
        {
            mapController = gameObject.AddComponent<MapController>();
        }
        
        // 设置组件引用
        tileSystem.Initialize(this, aMapAPI);
        mapController.Initialize(this);
        
        // 初始化UI
        InitializeUI();
        
        // 加载初始地图
        LoadMap(currentCenter, currentZoom);
    }
    
    /// <summary>
    /// 初始化UI组件
    /// </summary>
    private void InitializeUI()
    {
        if (zoomSlider != null)
        {
            zoomSlider.minValue = minZoom;
            zoomSlider.maxValue = maxZoom;
            zoomSlider.value = currentZoom;
            zoomSlider.onValueChanged.AddListener(OnZoomSliderChanged);
        }
        
        if (resetButton != null)
        {
            resetButton.onClick.AddListener(ResetMapView);
        }
        
        UpdateCoordinateDisplay();
    }
    
    /// <summary>
    /// 加载地图
    /// </summary>
    public void LoadMap(Vector2 center, float zoom)
    {
        currentCenter = center;
        currentZoom = Mathf.Clamp(zoom, minZoom, maxZoom);
        
        // 更新瓦片系统
        tileSystem.LoadTiles(center, currentZoom);
        
        // 更新UI
        UpdateUI();
    }
    
    /// <summary>
    /// 更新地图中心点
    /// </summary>
    public void UpdateMapCenter(Vector2 newCenter)
    {
        currentCenter = newCenter;
        LoadMap(currentCenter, currentZoom);
    }
    
    /// <summary>
    /// 更新缩放级别
    /// </summary>
    public void UpdateZoom(float newZoom)
    {
        float clampedZoom = Mathf.Clamp(newZoom, minZoom, maxZoom);
        if (Mathf.Abs(clampedZoom - currentZoom) > 0.1f)
        {
            LoadMap(currentCenter, clampedZoom);
        }
    }
    
    /// <summary>
    /// 缩放滑块变化事件
    /// </summary>
    private void OnZoomSliderChanged(float value)
    {
        UpdateZoom(value);
    }
    
    /// <summary>
    /// 重置地图视图到常熟市中心
    /// </summary>
    public void ResetMapView()
    {
        Vector2 changshuCenter = new Vector2(120.7478f, 31.6544f);
        LoadMap(changshuCenter, 10f);
    }
    
    /// <summary>
    /// 更新UI显示
    /// </summary>
    private void UpdateUI()
    {
        if (zoomSlider != null && Mathf.Abs(zoomSlider.value - currentZoom) > 0.1f)
        {
            zoomSlider.value = currentZoom;
        }
        
        UpdateCoordinateDisplay();
    }
    
    /// <summary>
    /// 更新坐标显示
    /// </summary>
    private void UpdateCoordinateDisplay()
    {
        if (coordinateText != null)
        {
            coordinateText.text = $"经度: {currentCenter.x:F4}, 纬度: {currentCenter.y:F4}, 缩放: {currentZoom:F1}";
        }
    }
    
    /// <summary>
    /// 获取当前地图边界
    /// </summary>
    public Bounds GetMapBounds()
    {
        // 根据当前缩放级别计算地图边界
        float latRange = 180f / Mathf.Pow(2f, currentZoom);
        float lonRange = 360f / Mathf.Pow(2f, currentZoom);
        
        Vector2 min = new Vector2(currentCenter.x - lonRange / 2f, currentCenter.y - latRange / 2f);
        Vector2 max = new Vector2(currentCenter.x + lonRange / 2f, currentCenter.y + latRange / 2f);
        
        Vector3 center = new Vector3((min.x + max.x) / 2f, (min.y + max.y) / 2f, 0f);
        Vector3 size = new Vector3(max.x - min.x, max.y - min.y, 0f);
        
        return new Bounds(center, size);
    }
    
    // 公共属性
    public float MinZoom => minZoom;
    public float MaxZoom => maxZoom;
    public int TileSize => tileSize;
    public Transform MapContainer => mapContainer;
    public Camera MapCamera => mapCamera;
}
