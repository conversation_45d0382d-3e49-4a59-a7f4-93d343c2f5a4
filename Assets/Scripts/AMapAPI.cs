using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;

/// <summary>
/// 高德地图API接口类
/// </summary>
public class AMapAPI : MonoBehaviour
{
    [Header("API配置")]
    [SerializeField] private string apiKey = "YOUR_AMAP_API_KEY"; // 需要替换为实际的高德地图API Key
    
    // 高德地图瓦片服务URL模板
    private const string TILE_URL_TEMPLATE = "https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={0}&y={1}&z={2}";
    
    // 高德地图地理编码API
    private const string GEOCODING_URL = "https://restapi.amap.com/v3/geocode/geo";
    
    // 高德地图逆地理编码API
    private const string REVERSE_GEOCODING_URL = "https://restapi.amap.com/v3/geocode/regeo";
    
    /// <summary>
    /// 获取地图瓦片
    /// </summary>
    /// <param name="x">瓦片X坐标</param>
    /// <param name="y">瓦片Y坐标</param>
    /// <param name="z">缩放级别</param>
    /// <param name="callback">回调函数</param>
    public void GetMapTile(int x, int y, int z, Action<Texture2D> callback)
    {
        string url = string.Format(TILE_URL_TEMPLATE, x, y, z);
        StartCoroutine(DownloadTile(url, callback));
    }
    
    /// <summary>
    /// 下载瓦片图片
    /// </summary>
    private IEnumerator DownloadTile(string url, Action<Texture2D> callback)
    {
        using (UnityWebRequest request = UnityWebRequestTexture.GetTexture(url))
        {
            // 设置请求头
            request.SetRequestHeader("User-Agent", "Unity Map Application");
            
            yield return request.SendWebRequest();
            
            if (request.result == UnityWebRequest.Result.Success)
            {
                Texture2D texture = DownloadHandlerTexture.GetContent(request);
                callback?.Invoke(texture);
            }
            else
            {
                Debug.LogError($"Failed to download tile: {url}, Error: {request.error}");
                callback?.Invoke(null);
            }
        }
    }
    
    /// <summary>
    /// 地理编码 - 将地址转换为坐标
    /// </summary>
    /// <param name="address">地址</param>
    /// <param name="callback">回调函数</param>
    public void Geocoding(string address, Action<Vector2> callback)
    {
        if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_AMAP_API_KEY")
        {
            Debug.LogWarning("请设置有效的高德地图API Key");
            callback?.Invoke(Vector2.zero);
            return;
        }
        
        string url = $"{GEOCODING_URL}?key={apiKey}&address={UnityWebRequest.EscapeURL(address)}";
        StartCoroutine(GeocodingRequest(url, callback));
    }
    
    /// <summary>
    /// 地理编码请求
    /// </summary>
    private IEnumerator GeocodingRequest(string url, Action<Vector2> callback)
    {
        using (UnityWebRequest request = UnityWebRequest.Get(url))
        {
            yield return request.SendWebRequest();
            
            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    GeocodingResponse response = JsonUtility.FromJson<GeocodingResponse>(request.downloadHandler.text);
                    if (response.status == "1" && response.geocodes != null && response.geocodes.Length > 0)
                    {
                        string[] location = response.geocodes[0].location.Split(',');
                        if (location.Length == 2)
                        {
                            float lon = float.Parse(location[0]);
                            float lat = float.Parse(location[1]);
                            callback?.Invoke(new Vector2(lon, lat));
                            yield break;
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to parse geocoding response: {e.Message}");
                }
            }
            else
            {
                Debug.LogError($"Geocoding request failed: {request.error}");
            }
            
            callback?.Invoke(Vector2.zero);
        }
    }
    
    /// <summary>
    /// 逆地理编码 - 将坐标转换为地址
    /// </summary>
    /// <param name="coordinate">坐标</param>
    /// <param name="callback">回调函数</param>
    public void ReverseGeocoding(Vector2 coordinate, Action<string> callback)
    {
        if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_AMAP_API_KEY")
        {
            Debug.LogWarning("请设置有效的高德地图API Key");
            callback?.Invoke("未知位置");
            return;
        }
        
        string location = $"{coordinate.x},{coordinate.y}";
        string url = $"{REVERSE_GEOCODING_URL}?key={apiKey}&location={location}";
        StartCoroutine(ReverseGeocodingRequest(url, callback));
    }
    
    /// <summary>
    /// 逆地理编码请求
    /// </summary>
    private IEnumerator ReverseGeocodingRequest(string url, Action<string> callback)
    {
        using (UnityWebRequest request = UnityWebRequest.Get(url))
        {
            yield return request.SendWebRequest();
            
            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    ReverseGeocodingResponse response = JsonUtility.FromJson<ReverseGeocodingResponse>(request.downloadHandler.text);
                    if (response.status == "1" && response.regeocode != null)
                    {
                        callback?.Invoke(response.regeocode.formatted_address);
                        yield break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to parse reverse geocoding response: {e.Message}");
                }
            }
            else
            {
                Debug.LogError($"Reverse geocoding request failed: {request.error}");
            }
            
            callback?.Invoke("未知位置");
        }
    }
    
    /// <summary>
    /// 经纬度转瓦片坐标
    /// </summary>
    public static Vector2Int LatLonToTileXY(Vector2 latLon, int zoom)
    {
        double lat = latLon.y * Math.PI / 180.0;
        double lon = latLon.x;
        
        int x = (int)Math.Floor((lon + 180.0) / 360.0 * Math.Pow(2.0, zoom));
        int y = (int)Math.Floor((1.0 - Math.Log(Math.Tan(lat) + 1.0 / Math.Cos(lat)) / Math.PI) / 2.0 * Math.Pow(2.0, zoom));
        
        return new Vector2Int(x, y);
    }
    
    /// <summary>
    /// 瓦片坐标转经纬度
    /// </summary>
    public static Vector2 TileXYToLatLon(Vector2Int tileXY, int zoom)
    {
        double n = Math.PI - 2.0 * Math.PI * tileXY.y / Math.Pow(2.0, zoom);
        double lat = 180.0 / Math.PI * Math.Atan(0.5 * (Math.Exp(n) - Math.Exp(-n)));
        double lon = tileXY.x / Math.Pow(2.0, zoom) * 360.0 - 180.0;
        
        return new Vector2((float)lon, (float)lat);
    }
    
    // 设置API Key
    public void SetApiKey(string key)
    {
        apiKey = key;
    }
}

// 地理编码响应数据结构
[Serializable]
public class GeocodingResponse
{
    public string status;
    public Geocode[] geocodes;
}

[Serializable]
public class Geocode
{
    public string location;
    public string formatted_address;
}

// 逆地理编码响应数据结构
[Serializable]
public class ReverseGeocodingResponse
{
    public string status;
    public Regeocode regeocode;
}

[Serializable]
public class Regeocode
{
    public string formatted_address;
}
