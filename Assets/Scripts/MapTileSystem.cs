using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 地图瓦片系统 - 负责瓦片的加载、缓存和显示
/// </summary>
public class MapTileSystem : MonoBehaviour
{
    [Header("瓦片配置")]
    public int maxCacheSize = 100;
    public float tileSize = 256f;
    public int loadRadius = 2; // 加载半径（瓦片数量）
    
    // 组件引用
    private MapManager mapManager;
    private AMapAPI aMapAPI;
    
    // 瓦片缓存
    private Dictionary<string, MapTile> tileCache = new Dictionary<string, MapTile>();
    private Queue<string> cacheOrder = new Queue<string>();
    
    // 当前显示的瓦片
    private Dictionary<string, GameObject> activeTiles = new Dictionary<string, GameObject>();
    
    // 瓦片预制体
    private GameObject tilePrefab;
    
    /// <summary>
    /// 初始化瓦片系统
    /// </summary>
    public void Initialize(MapManager manager, AMapAPI api)
    {
        mapManager = manager;
        aMapAPI = api;
        
        CreateTilePrefab();
    }
    
    /// <summary>
    /// 创建瓦片预制体
    /// </summary>
    private void CreateTilePrefab()
    {
        tilePrefab = new GameObject("TilePrefab");
        tilePrefab.AddComponent<SpriteRenderer>();
        tilePrefab.SetActive(false);
    }
    
    /// <summary>
    /// 加载瓦片
    /// </summary>
    public void LoadTiles(Vector2 center, float zoom)
    {
        int zoomLevel = Mathf.RoundToInt(zoom);
        Vector2Int centerTile = AMapAPI.LatLonToTileXY(center, zoomLevel);
        
        // 清除当前显示的瓦片
        ClearActiveTiles();
        
        // 加载新的瓦片
        for (int x = centerTile.x - loadRadius; x <= centerTile.x + loadRadius; x++)
        {
            for (int y = centerTile.y - loadRadius; y <= centerTile.y + loadRadius; y++)
            {
                LoadTile(x, y, zoomLevel);
            }
        }
    }
    
    /// <summary>
    /// 加载单个瓦片
    /// </summary>
    private void LoadTile(int x, int y, int z)
    {
        string tileKey = $"{x}_{y}_{z}";
        
        // 检查缓存
        if (tileCache.ContainsKey(tileKey))
        {
            DisplayTile(tileCache[tileKey], x, y, z);
            return;
        }
        
        // 从API加载瓦片
        aMapAPI.GetMapTile(x, y, z, (texture) =>
        {
            if (texture != null)
            {
                MapTile tile = new MapTile
                {
                    x = x,
                    y = y,
                    z = z,
                    texture = texture
                };
                
                // 添加到缓存
                AddToCache(tileKey, tile);
                
                // 显示瓦片
                DisplayTile(tile, x, y, z);
            }
        });
    }
    
    /// <summary>
    /// 显示瓦片
    /// </summary>
    private void DisplayTile(MapTile tile, int x, int y, int z)
    {
        string tileKey = $"{x}_{y}_{z}";
        
        if (activeTiles.ContainsKey(tileKey))
            return;
        
        // 创建瓦片游戏对象
        GameObject tileObject = Instantiate(tilePrefab, mapManager.MapContainer);
        tileObject.name = $"Tile_{x}_{y}_{z}";
        tileObject.SetActive(true);
        
        // 设置瓦片纹理
        SpriteRenderer renderer = tileObject.GetComponent<SpriteRenderer>();
        Sprite sprite = Sprite.Create(tile.texture, new Rect(0, 0, tile.texture.width, tile.texture.height), Vector2.one * 0.5f);
        renderer.sprite = sprite;
        
        // 计算瓦片位置
        Vector2 worldPos = CalculateTileWorldPosition(x, y, z);
        tileObject.transform.localPosition = new Vector3(worldPos.x, worldPos.y, 0);
        
        // 设置瓦片大小
        float scale = tileSize / tile.texture.width;
        tileObject.transform.localScale = Vector3.one * scale;
        
        // 添加到活动瓦片列表
        activeTiles[tileKey] = tileObject;
    }
    
    /// <summary>
    /// 计算瓦片在世界坐标系中的位置
    /// </summary>
    private Vector2 CalculateTileWorldPosition(int x, int y, int z)
    {
        // 将瓦片坐标转换为世界坐标
        Vector2 latLon = AMapAPI.TileXYToLatLon(new Vector2Int(x, y), z);
        
        // 转换为Unity世界坐标（这里使用简单的线性映射）
        float worldX = (latLon.x - mapManager.currentCenter.x) * 111320f; // 1度经度约等于111320米
        float worldY = (latLon.y - mapManager.currentCenter.y) * 110540f; // 1度纬度约等于110540米
        
        return new Vector2(worldX, worldY);
    }
    
    /// <summary>
    /// 添加到缓存
    /// </summary>
    private void AddToCache(string key, MapTile tile)
    {
        if (tileCache.ContainsKey(key))
            return;
        
        // 检查缓存大小
        if (tileCache.Count >= maxCacheSize)
        {
            // 移除最旧的瓦片
            string oldestKey = cacheOrder.Dequeue();
            if (tileCache.ContainsKey(oldestKey))
            {
                MapTile oldTile = tileCache[oldestKey];
                if (oldTile.texture != null)
                {
                    DestroyImmediate(oldTile.texture);
                }
                tileCache.Remove(oldestKey);
            }
        }
        
        // 添加新瓦片
        tileCache[key] = tile;
        cacheOrder.Enqueue(key);
    }
    
    /// <summary>
    /// 清除当前显示的瓦片
    /// </summary>
    private void ClearActiveTiles()
    {
        foreach (var kvp in activeTiles)
        {
            if (kvp.Value != null)
            {
                DestroyImmediate(kvp.Value);
            }
        }
        activeTiles.Clear();
    }
    
    /// <summary>
    /// 清除缓存
    /// </summary>
    public void ClearCache()
    {
        foreach (var kvp in tileCache)
        {
            if (kvp.Value.texture != null)
            {
                DestroyImmediate(kvp.Value.texture);
            }
        }
        tileCache.Clear();
        cacheOrder.Clear();
    }
    
    /// <summary>
    /// 获取缓存信息
    /// </summary>
    public string GetCacheInfo()
    {
        return $"缓存瓦片数: {tileCache.Count}/{maxCacheSize}, 显示瓦片数: {activeTiles.Count}";
    }
    
    void OnDestroy()
    {
        ClearCache();
        ClearActiveTiles();
    }
}

/// <summary>
/// 地图瓦片数据结构
/// </summary>
[System.Serializable]
public class MapTile
{
    public int x;
    public int y;
    public int z;
    public Texture2D texture;
}
