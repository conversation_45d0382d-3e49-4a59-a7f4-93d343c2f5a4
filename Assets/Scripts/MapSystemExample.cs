using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 地图系统示例 - 演示如何使用地图系统的各种功能
/// </summary>
public class MapSystemExample : MonoBehaviour
{
    [Header("示例配置")]
    public bool showExampleUI = true;
    public bool enableDebugInfo = true;
    
    [Header("示例地点")]
    public Vector2[] exampleLocations = new Vector2[]
    {
        new Vector2(120.7478f, 31.6544f), // 常熟市中心
        new Vector2(120.7058f, 31.6739f), // 常熟理工学院
        new Vector2(120.7167f, 31.6833f), // 虞山
        new Vector2(120.7000f, 31.7000f)  // 尚湖
    };
    
    // 组件引用
    private MapManager mapManager;
    private MapController mapController;
    private AMapAPI aMapAPI;
    private MapUI mapUI;
    
    // 示例UI
    private GameObject examplePanel;
    private Text debugText;
    
    void Start()
    {
        InitializeExample();
    }
    
    /// <summary>
    /// 初始化示例
    /// </summary>
    private void InitializeExample()
    {
        // 获取组件引用
        mapManager = FindObjectOfType<MapManager>();
        mapController = FindObjectOfType<MapController>();
        aMapAPI = FindObjectOfType<AMapAPI>();
        mapUI = FindObjectOfType<MapUI>();
        
        if (mapManager == null)
        {
            Debug.LogError("未找到MapManager组件，请先设置地图系统！");
            return;
        }
        
        // 创建示例UI
        if (showExampleUI)
        {
            CreateExampleUI();
        }
        
        // 设置调试信息
        if (enableDebugInfo)
        {
            SetupDebugInfo();
        }
        
        // 演示功能
        StartCoroutine(DemoFunctions());
    }
    
    /// <summary>
    /// 创建示例UI
    /// </summary>
    private void CreateExampleUI()
    {
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null) return;
        
        // 创建示例面板
        examplePanel = new GameObject("ExamplePanel");
        examplePanel.transform.SetParent(canvas.transform);
        
        RectTransform rect = examplePanel.AddComponent<RectTransform>();
        rect.anchorMin = new Vector2(0, 0);
        rect.anchorMax = new Vector2(0, 1);
        rect.anchoredPosition = new Vector2(150, 0);
        rect.sizeDelta = new Vector2(280, 0);
        
        Image panelImage = examplePanel.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        
        // 创建标题
        CreateExampleTitle();
        
        // 创建功能按钮
        CreateExampleButtons();
        
        // 创建调试文本
        if (enableDebugInfo)
        {
            CreateDebugText();
        }
    }
    
    /// <summary>
    /// 创建示例标题
    /// </summary>
    private void CreateExampleTitle()
    {
        GameObject title = new GameObject("Title");
        title.transform.SetParent(examplePanel.transform);
        
        Text titleText = title.AddComponent<Text>();
        titleText.text = "常熟市地图系统";
        titleText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        titleText.fontSize = 18;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = title.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 1);
        titleRect.anchorMax = new Vector2(1, 1);
        titleRect.anchoredPosition = new Vector2(0, -30);
        titleRect.sizeDelta = new Vector2(0, 40);
    }
    
    /// <summary>
    /// 创建示例按钮
    /// </summary>
    private void CreateExampleButtons()
    {
        string[] buttonNames = { "市中心", "理工学院", "虞山", "尚湖", "随机位置", "地理编码测试" };
        System.Action[] buttonActions = { 
            () => NavigateToLocation(0),
            () => NavigateToLocation(1),
            () => NavigateToLocation(2),
            () => NavigateToLocation(3),
            () => NavigateToRandomLocation(),
            () => TestGeocoding()
        };
        
        for (int i = 0; i < buttonNames.Length; i++)
        {
            CreateExampleButton(buttonNames[i], buttonActions[i], i);
        }
    }
    
    /// <summary>
    /// 创建示例按钮
    /// </summary>
    private void CreateExampleButton(string text, System.Action action, int index)
    {
        GameObject button = new GameObject($"Button_{text}");
        button.transform.SetParent(examplePanel.transform);
        
        Image buttonImage = button.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.6f, 1f, 0.8f);
        
        Button buttonComp = button.AddComponent<Button>();
        buttonComp.onClick.AddListener(() => action?.Invoke());
        
        // 按钮文本
        GameObject buttonText = new GameObject("Text");
        buttonText.transform.SetParent(button.transform);
        
        Text textComp = buttonText.AddComponent<Text>();
        textComp.text = text;
        textComp.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComp.fontSize = 14;
        textComp.color = Color.white;
        textComp.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = buttonText.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // 按钮位置
        RectTransform buttonRect = button.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0, 1);
        buttonRect.anchorMax = new Vector2(1, 1);
        buttonRect.anchoredPosition = new Vector2(0, -80 - index * 50);
        buttonRect.sizeDelta = new Vector2(-20, 40);
    }
    
    /// <summary>
    /// 创建调试文本
    /// </summary>
    private void CreateDebugText()
    {
        GameObject debugObj = new GameObject("DebugText");
        debugObj.transform.SetParent(examplePanel.transform);
        
        debugText = debugObj.AddComponent<Text>();
        debugText.text = "调试信息";
        debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        debugText.fontSize = 10;
        debugText.color = Color.yellow;
        debugText.alignment = TextAnchor.UpperLeft;
        
        RectTransform debugRect = debugObj.GetComponent<RectTransform>();
        debugRect.anchorMin = new Vector2(0, 0);
        debugRect.anchorMax = new Vector2(1, 0);
        debugRect.anchoredPosition = new Vector2(0, 100);
        debugRect.sizeDelta = new Vector2(-20, 150);
    }
    
    /// <summary>
    /// 设置调试信息
    /// </summary>
    private void SetupDebugInfo()
    {
        InvokeRepeating(nameof(UpdateDebugInfo), 1f, 1f);
    }
    
    /// <summary>
    /// 更新调试信息
    /// </summary>
    private void UpdateDebugInfo()
    {
        if (debugText == null || mapManager == null) return;
        
        string info = $"当前位置: {mapManager.currentCenter.x:F4}, {mapManager.currentCenter.y:F4}\n";
        info += $"缩放级别: {mapManager.currentZoom:F1}\n";
        info += $"FPS: {1f / Time.deltaTime:F0}\n";
        
        if (mapManager.GetComponent<MapTileSystem>() != null)
        {
            info += mapManager.GetComponent<MapTileSystem>().GetCacheInfo() + "\n";
        }
        
        info += $"内存使用: {System.GC.GetTotalMemory(false) / 1024 / 1024:F1} MB";
        
        debugText.text = info;
    }
    
    /// <summary>
    /// 导航到指定位置
    /// </summary>
    private void NavigateToLocation(int index)
    {
        if (index >= 0 && index < exampleLocations.Length && mapController != null)
        {
            mapController.MoveToWithZoom(exampleLocations[index], 15f);
            Debug.Log($"导航到位置 {index}: {exampleLocations[index]}");
        }
    }
    
    /// <summary>
    /// 导航到随机位置
    /// </summary>
    private void NavigateToRandomLocation()
    {
        if (mapController != null)
        {
            // 在常熟市周围生成随机坐标
            Vector2 randomPos = new Vector2(
                Random.Range(120.6f, 120.9f),
                Random.Range(31.5f, 31.8f)
            );
            
            mapController.MoveToWithZoom(randomPos, Random.Range(10f, 16f));
            Debug.Log($"导航到随机位置: {randomPos}");
        }
    }
    
    /// <summary>
    /// 测试地理编码
    /// </summary>
    private void TestGeocoding()
    {
        if (aMapAPI != null)
        {
            // 测试地理编码
            aMapAPI.Geocoding("常熟市虞山镇", (coordinate) =>
            {
                if (coordinate != Vector2.zero)
                {
                    Debug.Log($"地理编码结果: {coordinate}");
                    mapController?.MoveToWithZoom(coordinate, 14f);
                }
                else
                {
                    Debug.LogWarning("地理编码失败");
                }
            });
            
            // 测试逆地理编码
            aMapAPI.ReverseGeocoding(mapManager.currentCenter, (address) =>
            {
                Debug.Log($"当前位置地址: {address}");
                if (mapUI != null)
                {
                    mapUI.UpdateAddressDisplay(address);
                }
            });
        }
    }
    
    /// <summary>
    /// 演示功能协程
    /// </summary>
    private System.Collections.IEnumerator DemoFunctions()
    {
        yield return new WaitForSeconds(2f);
        
        Debug.Log("=== 常熟市地图系统演示开始 ===");
        Debug.Log("1. 地图已加载，显示常熟市中心");
        
        yield return new WaitForSeconds(3f);
        
        if (mapController != null)
        {
            Debug.Log("2. 演示缩放功能");
            mapController.MoveToWithZoom(mapManager.currentCenter, 12f);
        }
        
        yield return new WaitForSeconds(3f);
        
        Debug.Log("3. 演示完成，您可以使用左侧按钮测试各种功能");
    }
    
    void OnGUI()
    {
        if (!enableDebugInfo) return;
        
        // 显示简单的帮助信息
        GUI.Box(new Rect(10, 10, 200, 100), "控制说明:\n" +
            "鼠标拖拽: 移动地图\n" +
            "滚轮: 缩放地图\n" +
            "方向键: 移动地图\n" +
            "R键: 重置视图");
    }
}
