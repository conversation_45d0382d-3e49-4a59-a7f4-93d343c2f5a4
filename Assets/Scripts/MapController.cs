using UnityEngine;
using UnityEngine.EventSystems;

/// <summary>
/// 地图控制器 - 处理用户的地图交互操作
/// </summary>
public class MapController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerDownHand<PERSON>, IPointer<PERSON>p<PERSON><PERSON><PERSON>, IDragHandler, IScrollHandler
{
    [Header("交互配置")]
    public float dragSensitivity = 1f;
    public float zoomSensitivity = 0.5f;
    public float smoothTime = 0.3f;
    
    // 组件引用
    private MapManager mapManager;
    private Camera mapCamera;
    
    // 拖拽状态
    private bool isDragging = false;
    private Vector3 lastMousePosition;
    private Vector2 dragStartCenter;
    
    // 缩放状态
    private float targetZoom;
    private float zoomVelocity;
    
    // 移动状态
    private Vector2 targetCenter;
    private Vector2 centerVelocity;
    
    // 触摸输入（移动端支持）
    private Vector2[] lastTouchPositions = new Vector2[2];
    private float lastTouchDistance;
    
    /// <summary>
    /// 初始化控制器
    /// </summary>
    public void Initialize(MapManager manager)
    {
        mapManager = manager;
        mapCamera = mapManager.MapCamera;
        
        targetZoom = mapManager.currentZoom;
        targetCenter = mapManager.currentCenter;
    }
    
    void Update()
    {
        HandleInput();
        UpdateSmoothMovement();
    }
    
    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        // 处理键盘输入
        HandleKeyboardInput();
        
        // 处理触摸输入（移动端）
        HandleTouchInput();
    }
    
    /// <summary>
    /// 处理键盘输入
    /// </summary>
    private void HandleKeyboardInput()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        if (Mathf.Abs(horizontal) > 0.1f || Mathf.Abs(vertical) > 0.1f)
        {
            Vector2 movement = new Vector2(horizontal, vertical) * Time.deltaTime * 0.01f;
            targetCenter += movement;
            ClampTargetCenter();
        }
        
        // 缩放快捷键
        if (Input.GetKeyDown(KeyCode.Plus) || Input.GetKeyDown(KeyCode.KeypadPlus))
        {
            ZoomIn();
        }
        else if (Input.GetKeyDown(KeyCode.Minus) || Input.GetKeyDown(KeyCode.KeypadMinus))
        {
            ZoomOut();
        }
        
        // 重置快捷键
        if (Input.GetKeyDown(KeyCode.R))
        {
            mapManager.ResetMapView();
        }
    }
    
    /// <summary>
    /// 处理触摸输入
    /// </summary>
    private void HandleTouchInput()
    {
        if (Input.touchCount == 2)
        {
            // 双指缩放
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);
            
            Vector2 touch1Pos = touch1.position;
            Vector2 touch2Pos = touch2.position;
            
            float currentDistance = Vector2.Distance(touch1Pos, touch2Pos);
            
            if (touch1.phase == TouchPhase.Began || touch2.phase == TouchPhase.Began)
            {
                lastTouchDistance = currentDistance;
                lastTouchPositions[0] = touch1Pos;
                lastTouchPositions[1] = touch2Pos;
            }
            else if (touch1.phase == TouchPhase.Moved || touch2.phase == TouchPhase.Moved)
            {
                // 缩放
                float deltaDistance = currentDistance - lastTouchDistance;
                float zoomDelta = deltaDistance * zoomSensitivity * 0.01f;
                targetZoom = Mathf.Clamp(targetZoom + zoomDelta, mapManager.MinZoom, mapManager.MaxZoom);
                
                // 平移
                Vector2 currentCenter = (touch1Pos + touch2Pos) * 0.5f;
                Vector2 lastCenter = (lastTouchPositions[0] + lastTouchPositions[1]) * 0.5f;
                Vector2 deltaCenter = (currentCenter - lastCenter) * dragSensitivity * 0.0001f;
                targetCenter -= deltaCenter;
                ClampTargetCenter();
                
                lastTouchDistance = currentDistance;
                lastTouchPositions[0] = touch1Pos;
                lastTouchPositions[1] = touch2Pos;
            }
        }
    }
    
    /// <summary>
    /// 更新平滑移动
    /// </summary>
    private void UpdateSmoothMovement()
    {
        // 平滑缩放
        float newZoom = Mathf.SmoothDamp(mapManager.currentZoom, targetZoom, ref zoomVelocity, smoothTime);
        if (Mathf.Abs(newZoom - mapManager.currentZoom) > 0.01f)
        {
            mapManager.UpdateZoom(newZoom);
        }
        
        // 平滑移动
        Vector2 newCenter = Vector2.SmoothDamp(mapManager.currentCenter, targetCenter, ref centerVelocity, smoothTime);
        if (Vector2.Distance(newCenter, mapManager.currentCenter) > 0.0001f)
        {
            mapManager.UpdateMapCenter(newCenter);
        }
    }
    
    /// <summary>
    /// 指针按下事件
    /// </summary>
    public void OnPointerDown(PointerEventData eventData)
    {
        if (eventData.button == PointerEventData.InputButton.Left)
        {
            isDragging = true;
            lastMousePosition = Input.mousePosition;
            dragStartCenter = mapManager.currentCenter;
        }
    }
    
    /// <summary>
    /// 指针抬起事件
    /// </summary>
    public void OnPointerUp(PointerEventData eventData)
    {
        if (eventData.button == PointerEventData.InputButton.Left)
        {
            isDragging = false;
        }
    }
    
    /// <summary>
    /// 拖拽事件
    /// </summary>
    public void OnDrag(PointerEventData eventData)
    {
        if (isDragging)
        {
            Vector3 currentMousePosition = Input.mousePosition;
            Vector3 deltaPosition = currentMousePosition - lastMousePosition;
            
            // 将屏幕坐标转换为地图坐标
            Vector2 worldDelta = ScreenToWorldDelta(deltaPosition);
            targetCenter = dragStartCenter - worldDelta;
            ClampTargetCenter();
            
            lastMousePosition = currentMousePosition;
        }
    }
    
    /// <summary>
    /// 滚轮事件
    /// </summary>
    public void OnScroll(PointerEventData eventData)
    {
        float scrollDelta = eventData.scrollDelta.y;
        float zoomDelta = scrollDelta * zoomSensitivity;
        targetZoom = Mathf.Clamp(targetZoom + zoomDelta, mapManager.MinZoom, mapManager.MaxZoom);
    }
    
    /// <summary>
    /// 屏幕坐标转世界坐标差值
    /// </summary>
    private Vector2 ScreenToWorldDelta(Vector3 screenDelta)
    {
        // 根据当前缩放级别计算转换比例
        float scale = Mathf.Pow(2f, mapManager.currentZoom - 10f);
        float sensitivity = dragSensitivity / (scale * 10000f);
        
        return new Vector2(screenDelta.x * sensitivity, screenDelta.y * sensitivity);
    }
    
    /// <summary>
    /// 限制目标中心点范围
    /// </summary>
    private void ClampTargetCenter()
    {
        // 限制在合理的地理范围内
        targetCenter.x = Mathf.Clamp(targetCenter.x, -180f, 180f);
        targetCenter.y = Mathf.Clamp(targetCenter.y, -85f, 85f);
    }
    
    /// <summary>
    /// 放大
    /// </summary>
    public void ZoomIn()
    {
        targetZoom = Mathf.Clamp(targetZoom + 1f, mapManager.MinZoom, mapManager.MaxZoom);
    }
    
    /// <summary>
    /// 缩小
    /// </summary>
    public void ZoomOut()
    {
        targetZoom = Mathf.Clamp(targetZoom - 1f, mapManager.MinZoom, mapManager.MaxZoom);
    }
    
    /// <summary>
    /// 移动到指定位置
    /// </summary>
    public void MoveTo(Vector2 coordinate)
    {
        targetCenter = coordinate;
        ClampTargetCenter();
    }
    
    /// <summary>
    /// 移动到指定位置并缩放
    /// </summary>
    public void MoveToWithZoom(Vector2 coordinate, float zoom)
    {
        targetCenter = coordinate;
        targetZoom = Mathf.Clamp(zoom, mapManager.MinZoom, mapManager.MaxZoom);
        ClampTargetCenter();
    }
}
